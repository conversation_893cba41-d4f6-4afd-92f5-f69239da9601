server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location ~ \.(js|css|png|jpg|svg|ico|webp|woff|woff2|ttf|eot|otf|json)$ {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "no-referrer-when-downgrade";
    add_header X-XSS-Protection "1; mode=block";
}
