import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  IconButton,
  Button,
  TextField,
  Divider,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Add,
  Remove,
  Delete,
  ShoppingCart,
  Payment,
} from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import {
  setLoading,
  setCartItems,
  updateCartItem,
  removeCartItem,
  clearCart,
} from "../../stores/reducers/Cart";
import { cartAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const CartPage = () => {
  const { items, totalItems, totalAmount, loading } = useAppSelector((state) => state.Cart);
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [quantities, setQuantities] = useState<{ [key: number]: number }>({});

  useEffect(() => {
    if (isAuthenticated) {
      loadCart();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Initialize quantities from cart items
    const initialQuantities: { [key: number]: number } = {};
    items.forEach(item => {
      initialQuantities[item.productId] = item.quantity;
    });
    setQuantities(initialQuantities);
  }, [items]);

  const loadCart = async () => {
    try {
      dispatch(setLoading(true));
      console.log('Loading cart...');

      // Check if user is authenticated
      if (!isAuthenticated) {
        console.log('User not authenticated, clearing cart');
        dispatch(clearCart());
        return;
      }

      // Clear current cart state first
      dispatch(clearCart());

      const cartData = await cartAPI.getCart();
      console.log('Raw cart data from API:', cartData);

      if (Array.isArray(cartData) && cartData.length > 0) {
        dispatch(setCartItems(cartData));
      } else {
        console.log('No cart items found');
        dispatch(setCartItems([]));
      }
    } catch (error: any) {
      console.error('Error loading cart:', error);

      // If it's an auth error, redirect to login
      if (error.message.includes('500') || error.message.includes('User ID claim not found')) {
        console.log('Authentication error, clearing cart and redirecting to login');
        dispatch(clearCart());
        toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
        navigate('/auth/login');
      } else {
        toast.error(`Không thể tải giỏ hàng: ${error.message}`);
        dispatch(setCartItems([]));
      }
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleQuantityChange = async (productId: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    // Update local state immediately for better UX
    setQuantities(prev => ({
      ...prev,
      [productId]: newQuantity
    }));

    try {
      console.log('Updating quantity for product', productId, 'to', newQuantity);

      // Update quantity on backend using updateItem
      await cartAPI.updateItem(productId, newQuantity);

      // Reload cart to get accurate data
      const cartData = await cartAPI.getCart();
      dispatch(setCartItems(cartData));

      console.log('Quantity updated successfully');

    } catch (error: any) {
      console.error('Error updating cart quantity:', error);
      toast.error(`Không thể cập nhật số lượng: ${error.message}`);

      // Revert local state on error
      loadCart();
    }
  };

  const handleRemoveItem = async (productId: number) => {
    try {
      await cartAPI.removeItem(productId);

      // Reload cart to get accurate data
      const cartData = await cartAPI.getCart();
      dispatch(setCartItems(cartData));

      toast.success("Đã xóa sản phẩm khỏi giỏ hàng");
    } catch (error: any) {
      console.error('Error removing cart item:', error);
      toast.error(`Không thể xóa sản phẩm: ${error.message}`);
    }
  };

  const handleClearCart = async () => {
    try {
      // Remove all items from backend
      for (const item of items) {
        await cartAPI.removeItem(item.productId);
      }

      // Clear Redux store
      dispatch(clearCart());

      // Reset local quantities state
      setQuantities({});

      toast.success("Đã xóa tất cả sản phẩm");
    } catch (error: any) {
      console.error('Error clearing cart:', error);
      toast.error(`Không thể xóa giỏ hàng: ${error.message}`);
    }
  };

  const handleResetCart = () => {
    // Emergency reset - clear all cart data
    dispatch(clearCart());
    setQuantities({});
    localStorage.removeItem('persist:root'); // Clear persisted state
    toast.success("Đã reset giỏ hàng");
    window.location.reload(); // Reload to ensure clean state
  };

  const handleCheckout = () => {
    if (items.length === 0) {
      toast.warning("Giỏ hàng trống");
      return;
    }
    navigate("/checkout");
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem giỏ hàng
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: "center" }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h4">
          <ShoppingCart sx={{ mr: 2, verticalAlign: "middle" }} />
          Giỏ hàng của bạn
        </Typography>
        <Button
          variant="outlined"
          color="warning"
          onClick={handleResetCart}
          size="small"
        >
          Reset Giỏ Hàng
        </Button>
      </Box>

      {items.length === 0 ? (
        <Paper elevation={2} sx={{ p: 4, textAlign: "center" }}>
          <ShoppingCart sx={{ fontSize: 80, color: "grey.400", mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Giỏ hàng trống
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate("/flower")}
          >
            Tiếp tục mua sắm
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper elevation={2} sx={{ p: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Sản phẩm ({totalItems} món)
                </Typography>
                <Button
                  variant="outlined"
                  color="error"
                  size="small"
                  onClick={handleClearCart}
                >
                  Xóa tất cả
                </Button>
              </Box>

              {items.map((item) => (
                <Card key={item.id} sx={{ mb: 2, display: "flex" }}>
                  <CardMedia
                    component="img"
                    sx={{ width: 120, height: 120 }}
                    image={item.product?.imageUrl || "/placeholder.jpg"}
                    alt={item.product?.name}
                  />
                  <CardContent sx={{ flex: 1 }}>
                    <Typography variant="h6" gutterBottom>
                      {item.product?.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {item.product?.description}
                    </Typography>
                    <Typography variant="h6" color="primary">
                      {item.product?.price?.toLocaleString("vi-VN")}đ
                    </Typography>
                  </CardContent>
                  <Box sx={{ p: 2, display: "flex", flexDirection: "column", justifyContent: "space-between" }}>
                    <Box display="flex" alignItems="center" mb={2}>
                      <IconButton
                        size="small"
                        onClick={() => {
                          const currentQty = quantities[item.productId] || item.quantity;
                          handleQuantityChange(item.productId, Math.max(1, currentQty - 1));
                        }}
                        disabled={(quantities[item.productId] || item.quantity) <= 1}
                      >
                        <Remove />
                      </IconButton>
                      <TextField
                        size="small"
                        value={quantities[item.productId] || item.quantity}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 1;
                          if (value >= 1) {
                            handleQuantityChange(item.productId, value);
                          }
                        }}
                        sx={{ width: 60, mx: 1 }}
                        inputProps={{ min: 1, style: { textAlign: "center" } }}
                        type="number"
                      />
                      <IconButton
                        size="small"
                        onClick={() => {
                          const currentQty = quantities[item.productId] || item.quantity;
                          handleQuantityChange(item.productId, currentQty + 1);
                        }}
                      >
                        <Add />
                      </IconButton>
                    </Box>
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveItem(item.productId)}
                    >
                      <Delete />
                    </IconButton>
                  </Box>
                </Card>
              ))}
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper elevation={2} sx={{ p: 3, position: "sticky", top: 20 }}>
              <Typography variant="h6" gutterBottom>
                Tóm tắt đơn hàng
              </Typography>

              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography>Tạm tính:</Typography>
                <Typography>{totalAmount.toLocaleString("vi-VN")}đ</Typography>
              </Box>

              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography>Phí vận chuyển:</Typography>
                <Typography>Miễn phí</Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" mb={3}>
                <Typography variant="h6">Tổng cộng:</Typography>
                <Typography variant="h6" color="primary">
                  {totalAmount.toLocaleString("vi-VN")}đ
                </Typography>
              </Box>

              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<Payment />}
                onClick={handleCheckout}
                sx={{ mb: 2 }}
              >
                Thanh toán
              </Button>

              <Button
                fullWidth
                variant="outlined"
                onClick={() => navigate("/flower")}
              >
                Tiếp tục mua sắm
              </Button>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default CartPage;
